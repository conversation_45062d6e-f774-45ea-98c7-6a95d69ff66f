"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { StackRef } from "@/components/ui/stack";
import { ArrowLeft, Edit3, Plus, Trash2, Wand2, Loader2, Play, Pause, Languages, AudioLines, RotateCcw } from "lucide-react";
import { useTranslations } from "next-intl";
import { RefObject, useState, useEffect, useRef, useCallback, useMemo } from "react";
import Image from "next/image";

import { useAtomValue, useSetAtom } from "jotai";
import {
  subtitleDataAtom,
  addSubtitleAtom,
  updateBothSubtitlesAtom,
  deleteSubtitleAtom,
  clearSubtitlesAtom,
  SubtitleItem,
  SubtitleData
} from "@/stores/slices/subtitle_store";
import { currentTaskAtom } from "@/stores/slices/current_task";
import { ArtPlayer } from "@/components/common/art-player";

import { isYoutubeUrl, getYoutubeVideoId } from "@/utils/video-format";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import { env } from "@/env";

interface SubtitleEditorWithVideoStackProps {
  stackRef: RefObject<StackRef>;
}

interface SubtitleRowProps {
  subtitle: SubtitleItem;
  editingField: string | null;
  onFieldEdit: (field: string | null) => void;
  onSave: (updates: Partial<SubtitleItem>) => void;
  onDelete: () => void;
  isActive: boolean;
  onSeekTo: (time: number) => void;
}

// Convert time string to seconds
const timeStringToSeconds = (timeString: string): number => {
  const [time, milliseconds] = timeString.split(',');
  const [hours, minutes, seconds] = time.split(':').map(Number);
  return hours * 3600 + minutes * 60 + seconds + (parseInt(milliseconds || '0') / 1000);
};

// Convert seconds to time string
const secondsToTimeString = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  const milliseconds = Math.floor((seconds % 1) * 1000);
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${milliseconds.toString().padStart(3, '0')}`;
};

// SRT Parser
const parseSrtToSubtitleItems = (srtContent: string): SubtitleItem[] => {
  console.log('[PARSER] Starting to parse SRT content, length:', srtContent.length);
  const items: SubtitleItem[] = [];
  const lines = srtContent.split(/\r?\n/);
  let i = 0;
  while (i < lines.length) {
    const idLine = lines[i];
    if (!idLine || /^\s*$/.test(idLine)) { // Skip empty lines between entries
      i++;
      continue;
    }

    // Skip index number line (optional, we generate our own IDs)
    // const index = parseInt(idLine, 10);
    i++;
    if (i >= lines.length) break;

    const timeLine = lines[i];
    const timeMatch = timeLine.match(/(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})/);
    if (!timeMatch) {
      console.log(`[PARSER] Invalid time format at line ${i}: "${timeLine}"`);
      // Invalid time format, skip this entry or handle error
      // For now, try to find next valid entry
      while(i < lines.length && !lines[i].match(/^\d+$/) && !lines[i].match(/(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})/)) {
        i++;
      }
      if (i < lines.length && lines[i].match(/^\d+$/)) { // Found next index
        // continue to next iteration
      } else {
        i++; // skip current line and continue
      }
      continue;
    }
    const startTime = timeMatch[1];
    const endTime = timeMatch[2];
    i++;

    const textLines: string[] = [];
    while (i < lines.length && lines[i]) {
      textLines.push(lines[i]);
      i++;
    }

    let text = "";
    let translatedText = "";

    if (textLines.length === 1) {
      text = textLines[0];
    } else if (textLines.length >= 2) {
      text = textLines[0]; // First line as original
      translatedText = textLines.slice(1).join('\n'); // Rest as translation
    }

    if (text || translatedText) { // Only add if there's some text
        const item = {
            id: crypto.randomUUID(),
            startTime,
            endTime,
            text,
            translatedText: translatedText || undefined,
        };
        items.push(item);
    }

    // Skip empty line after text block if present
    if (i < lines.length && !lines[i]) {
      i++;
    }
  }
  console.log('[PARSER] Final parsed items count:', items.length);
  return items;
};

// Parse translated SRT content (contains only target language)
const parseTranslatedSrtToSubtitleItems = (srtContent: string): SubtitleItem[] => {
  console.log('[TRANSLATE_PARSER] Starting to parse translated SRT content, length:', srtContent.length);
  const items: SubtitleItem[] = [];
  const lines = srtContent.split(/\r?\n/);
  let i = 0;
  while (i < lines.length) {
    const idLine = lines[i];
    if (!idLine || /^\s*$/.test(idLine)) { // Skip empty lines between entries
      i++;
      continue;
    }

    // Skip index number line (optional, we generate our own IDs)
    i++;
    if (i >= lines.length) break;

    const timeLine = lines[i];
    const timeMatch = timeLine.match(/(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})/);
    if (!timeMatch) {
      console.log(`[TRANSLATE_PARSER] Invalid time format at line ${i}: "${timeLine}"`);
      // Invalid time format, skip this entry or handle error
      while(i < lines.length && !lines[i].match(/^\d+$/) && !lines[i].match(/(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})/)) {
        i++;
      }
      if (i < lines.length && lines[i].match(/^\d+$/)) { // Found next index
        // continue to next iteration
      } else {
        i++; // skip current line and continue
      }
      continue;
    }
    const startTime = timeMatch[1];
    const endTime = timeMatch[2];
    i++;

    const textLines: string[] = [];
    while (i < lines.length && lines[i]) {
      textLines.push(lines[i]);
      i++;
    }

    // For translated SRT, all text goes to translatedText field
    const translatedText = textLines.join('\n').trim();

    if (translatedText) { // Only add if there's translated text
        const item = {
            id: crypto.randomUUID(),
            startTime,
            endTime,
            text: "", // Empty original text since this is translated content
            translatedText,
        };
        items.push(item);
    }

    // Skip empty line after text block if present
    if (i < lines.length && !lines[i]) {
      i++;
    }
  }
  console.log('[TRANSLATE_PARSER] Final parsed translated items count:', items.length);
  return items;
};

// Generate SRT content from subtitle items
const generateSrtContent = (subtitles: SubtitleItem[]): string => {
  // Filter out subtitles without text content
  const subtitlesWithText = subtitles.filter(subtitle => subtitle.text.trim());

  return subtitlesWithText.map((subtitle, index) => {
    const lines = [
      (index + 1).toString(),
      `${subtitle.startTime} --> ${subtitle.endTime}`,
      subtitle.text.trim()
    ];
    return lines.join('\n');
  }).join('\n\n');
};

// Upload SRT file and return the URL
const uploadSrtFile = async (srtContent: string, apiHost: string): Promise<string> => {
  const uploadEndpoint = `${apiHost}/upload`;

  // Create a blob from the SRT content
  const blob = new Blob([srtContent], { type: 'text/plain' });
  const file = new File([blob], 'subtitles.srt', { type: 'text/plain' });

  // Create FormData for file upload
  const formData = new FormData();
  formData.append('file', file);

  console.log('[UPLOAD] Uploading SRT file to:', uploadEndpoint);
  console.log('[UPLOAD] File size:', blob.size, 'bytes');

  const response = await fetch(uploadEndpoint, {
    method: 'POST',
    body: formData,
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: "Unknown error occurred" }));
    throw new Error(`Upload failed: ${response.status} ${response.statusText}. ${errorData.message || ''}`);
  }

  const responseData = await response.json();
  console.log('[UPLOAD] Upload response:', responseData);

  // Check for expected response format: {code: 0, msg: 'ok', data: {url: file_url}}
  if (responseData.code === 0 && responseData.data && responseData.data.url) {
    return responseData.data.url;
  } else {
    throw new Error(`Invalid upload response: ${JSON.stringify(responseData)}`);
  }
};


const SubtitleRow = ({
  subtitle,
  editingField, 
  onFieldEdit, 
  onSave, 
  onDelete,
  isActive,
  onSeekTo
}: SubtitleRowProps) => {
  const [editData, setEditData] = useState({
    startTime: subtitle.startTime,
    endTime: subtitle.endTime,
    text: subtitle.text,
    translatedText: subtitle.translatedText || "",
  });

  // Update edit data when subtitle changes
  useEffect(() => {
    setEditData({
      startTime: subtitle.startTime,
      endTime: subtitle.endTime,
      text: subtitle.text,
      translatedText: subtitle.translatedText || "",
    });
  }, [subtitle]);

  const handleSave = (field: string) => {
    const updates: Partial<SubtitleItem> = {};
    
    switch (field) {
      case 'startTime':
        updates.startTime = editData.startTime;
        break;
      case 'endTime':
        updates.endTime = editData.endTime;
        break;
      case 'text':
        updates.text = editData.text;
        break;
      case 'translatedText':
        updates.translatedText = editData.translatedText;
        break;
    }
    
    onSave(updates);
    onFieldEdit(null);
  };

  const handleCancel = () => {
    setEditData({
      startTime: subtitle.startTime,
      endTime: subtitle.endTime,
      text: subtitle.text,
      translatedText: subtitle.translatedText || "",
    });
    onFieldEdit(null);
  };

  const handleKeyDown = (e: React.KeyboardEvent, field: string) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSave(field);
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  };

  const handleSeekToStart = () => {
    const startSeconds = timeStringToSeconds(subtitle.startTime);
    onSeekTo(startSeconds);
  };

  return (
    <div
      className={cn(
        "grid grid-cols-6 gap-2 p-3 border-b hover:bg-muted/30 transition-colors cursor-pointer",
        editingField && "bg-primary/5 border-primary/20",
        isActive && "bg-primary/10 border-primary/30 shadow-sm"
      )}
      onClick={handleSeekToStart}
      data-subtitle-id={subtitle.id}
    >
      {/* Start Time */}
      <div className="flex flex-col gap-1">
        {editingField === 'startTime' ? (
          <Input
            value={editData.startTime}
            onChange={(e) => setEditData(prev => ({ ...prev, startTime: e.target.value }))}
            onKeyDown={(e) => handleKeyDown(e, 'startTime')}
            onBlur={() => handleSave('startTime')}
            className="h-8 text-xs"
            placeholder="00:00:00,000"
            autoFocus
            onClick={(e) => e.stopPropagation()}
          />
        ) : (
          <div 
            className="text-xs font-mono bg-muted/50 px-2 py-1 rounded cursor-pointer hover:bg-muted transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              onFieldEdit('startTime');
            }}
          >
            {subtitle.startTime}
          </div>
        )}
      </div>

      {/* End Time */}
      <div className="flex flex-col gap-1">
        {editingField === 'endTime' ? (
          <Input
            value={editData.endTime}
            onChange={(e) => setEditData(prev => ({ ...prev, endTime: e.target.value }))}
            onKeyDown={(e) => handleKeyDown(e, 'endTime')}
            onBlur={() => handleSave('endTime')}
            className="h-8 text-xs"
            placeholder="00:00:03,000"
            autoFocus
            onClick={(e) => e.stopPropagation()}
          />
        ) : (
          <div 
            className="text-xs font-mono bg-muted/50 px-2 py-1 rounded cursor-pointer hover:bg-muted transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              onFieldEdit('endTime');
            }}
          >
            {subtitle.endTime}
          </div>
        )}
      </div>

      {/* Original Text */}
      <div className="flex flex-col gap-1">
        {editingField === 'text' ? (
          <Textarea
            value={editData.text}
            onChange={(e) => setEditData(prev => ({ ...prev, text: e.target.value }))}
            onKeyDown={(e) => handleKeyDown(e, 'text')}
            onBlur={() => handleSave('text')}
            className="min-h-[60px] text-sm resize-none"
            placeholder="Enter subtitle text..."
            autoFocus
            onClick={(e) => e.stopPropagation()}
          />
        ) : (
          <div 
            className="text-sm leading-relaxed p-2 min-h-[60px] bg-background border rounded cursor-pointer hover:bg-muted/20 transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              onFieldEdit('text');
            }}
          >
            {subtitle.text || (
              <span className="text-muted-foreground italic">Click to add text...</span>
            )}
          </div>
        )}
      </div>

      {/* Translated Text */}
      <div className="flex flex-col gap-1">
        {editingField === 'translatedText' ? (
          <Textarea
            value={editData.translatedText}
            onChange={(e) => setEditData(prev => ({ ...prev, translatedText: e.target.value }))}
            onKeyDown={(e) => handleKeyDown(e, 'translatedText')}
            onBlur={() => handleSave('translatedText')}
            className="min-h-[60px] text-sm resize-none"
            placeholder="Enter translation..."
            autoFocus
            onClick={(e) => e.stopPropagation()}
          />
        ) : (
          <div 
            className="text-sm leading-relaxed p-2 min-h-[60px] bg-background border rounded cursor-pointer hover:bg-muted/20 transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              onFieldEdit('translatedText');
            }}
          >
            {subtitle.translatedText || (
              <span className="text-muted-foreground italic">Click to add translation...</span>
            )}
          </div>
        )}
      </div>

      {/* Duration */}
      <div className="flex flex-col gap-1 justify-center">
        <div className="text-xs text-muted-foreground text-center">
          {((timeStringToSeconds(subtitle.endTime) - timeStringToSeconds(subtitle.startTime))).toFixed(1)}s
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-start gap-1">
        <div className="flex flex-col gap-1">
          <Button 
            size="sm" 
            variant="ghost" 
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }} 
            className="h-7"
          >
            <Trash2 className="h-3 w-3" />
          </Button>
          {editingField && (
            <Button 
              size="sm" 
              variant="ghost" 
              onClick={(e) => {
                e.stopPropagation();
                handleCancel();
              }} 
              className="h-7 text-xs"
            >
              ESC
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export const SubtitleEditorWithVideoStack = ({ stackRef }: SubtitleEditorWithVideoStackProps) => {
  console.log('[COMPONENT] SubtitleEditorWithVideoStack render');

  const t = useTranslations();
  const subtitleData = useAtomValue(subtitleDataAtom);
  const setSubtitleData = useSetAtom(subtitleDataAtom);
  const currentTask = useAtomValue(currentTaskAtom);
  const addSubtitle = useSetAtom(addSubtitleAtom);
  const updateBothSubtitles = useSetAtom(updateBothSubtitlesAtom);
  const deleteSubtitle = useSetAtom(deleteSubtitleAtom);
  const clearSubtitles = useSetAtom(clearSubtitlesAtom);

  // State for tracking which field is being edited
  const [editingField, setEditingField] = useState<{id: string, field: string} | null>(null);
  const [currentTime, setCurrentTime] = useState(0);

  // AI prompt state
  const [showPromptInput, setShowPromptInput] = useState(false);
  const [promptText, setPromptText] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [isTranslating, setIsTranslating] = useState(false);
  const [isExtracting, setIsExtracting] = useState(false);
  const [extractionStatus, setExtractionStatus] = useState<string>("");
  const [translationStatus, setTranslationStatus] = useState<string>("");

  // Subtitle overlay state
  const [currentSubtitle, setCurrentSubtitle] = useState<SubtitleItem | null>(null);

  // Refs for video player and subtitle list
  const artPlayerRef = useRef<any>(null);
  const subtitleListRef = useRef<HTMLDivElement>(null);
  const videoContainerRef = useRef<HTMLDivElement>(null);
  const previousVideoUrlRef = useRef<string | undefined>(undefined);

  // Simple timer state for subtitle preview (independent of video)
  const [isTimerRunning, setIsTimerRunning] = useState(false);
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const [videoReady, setVideoReady] = useState(false);
  const [isVideoFollowing, setIsVideoFollowing] = useState(false);

  // Manual timer fallback (only when video is not available)
  useEffect(() => {
    // Only use manual timer if no video player exists or video is not ready
    if (isTimerRunning && (!artPlayerRef.current || !videoReady)) {
      console.log('[TIMER] Starting manual timer - isTimerRunning:', isTimerRunning, 'videoReady:', videoReady, 'hasArtPlayer:', !!artPlayerRef.current);
      const interval = setInterval(() => {
        setCurrentTime(prev => {
          const newTime = prev + 0.1;
          console.log('[TIMER] Manual timer update:', newTime.toFixed(1));
          return newTime;
        });
      }, 100);
      return () => {
        console.log('[TIMER] Stopping manual timer');
        clearInterval(interval);
      };
    } else {
      console.log('[TIMER] Manual timer not started - isTimerRunning:', isTimerRunning, 'videoReady:', videoReady, 'hasArtPlayer:', !!artPlayerRef.current);
    }
  }, [isTimerRunning, videoReady]);

  // Find current subtitle based on video time
  const findCurrentSubtitle = (time: number): SubtitleItem | null => {
    return subtitleData.originalSubtitles.find(subtitle => {
      const startTime = timeStringToSeconds(subtitle.startTime);
      const endTime = timeStringToSeconds(subtitle.endTime);
      return time >= startTime && time <= endTime;
    }) || null;
  };

  // Update current subtitle when time changes
  useEffect(() => {
    const newCurrentSubtitle = findCurrentSubtitle(currentTime);
    setCurrentSubtitle(newCurrentSubtitle);
  }, [currentTime, subtitleData.originalSubtitles]);

  // Reset video state when video URL changes
  useEffect(() => {
    console.log('[VIDEO] Video URL changed, resetting states. New URL:', currentTask.videoUrl);
    setVideoReady(false);
    setIsVideoPlaying(false);
    setIsTimerRunning(false);
  }, [currentTask.videoUrl]);

  // Clear subtitle editor when a new video is uploaded
  useEffect(() => {
    // Only clear subtitles if this is a new video upload (not the initial load)
    if (previousVideoUrlRef.current !== undefined &&
        previousVideoUrlRef.current !== currentTask.videoUrl &&
        currentTask.videoUrl) {

      console.log('[SUBTITLE_CLEAR] New video detected, clearing subtitle editor');
      console.log('[SUBTITLE_CLEAR] Previous URL:', previousVideoUrlRef.current);
      console.log('[SUBTITLE_CLEAR] New URL:', currentTask.videoUrl);

      // Clear all subtitle data
      clearSubtitles();

      // Reset editing states
      setEditingField(null);
      setCurrentTime(0);
      setShowPromptInput(false);
      setPromptText("");

      // Reset processing states
      setIsGenerating(false);
      setIsTranslating(false);
      setIsExtracting(false);
      setTranslationStatus("");
      setExtractionStatus("");

      toast.success("Subtitle editor cleared for new video");
    }

    // Update the previous URL reference
    previousVideoUrlRef.current = currentTask.videoUrl;
  }, [currentTask.videoUrl, clearSubtitles]);

  // Auto-scroll subtitle list to current subtitle (with debouncing)
  useEffect(() => {
    if (currentSubtitle && subtitleListRef.current) {
      // Debounce the scroll to prevent too frequent updates
      const timeoutId = setTimeout(() => {
        try {
          const subtitleElement = subtitleListRef.current?.querySelector(`[data-subtitle-id="${currentSubtitle.id}"]`);
          if (subtitleElement) {
            subtitleElement.scrollIntoView({
              behavior: 'smooth',
              block: 'center'
            });
          }
        } catch (error) {
          console.warn('Failed to scroll to subtitle:', error);
        }
      }, 200);

      return () => clearTimeout(timeoutId);
    }
  }, [currentSubtitle]);

  // Video scroll following effect
  useEffect(() => {
    const subtitleList = subtitleListRef.current;
    if (!subtitleList) return;

    let scrollTimeout: NodeJS.Timeout;

    const handleScroll = () => {
      // Set isVideoFollowing to true immediately on scroll
      setIsVideoFollowing(true);
      // console.log('[SCROLL] Subtitle scroll detected, video following styles applied');

      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        setIsVideoFollowing(false);
        // console.log('[SCROLL] Scroll stopped, video following styles removed');
      }, 1000); // Reset after 1 second of no scrolling
    };

    subtitleList.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      subtitleList.removeEventListener('scroll', handleScroll);
      clearTimeout(scrollTimeout);
    };
  }, []);

  // Handle adding subtitle at current time
  const handleAddSubtitle = () => {
    const startTime = secondsToTimeString(currentTime);
    const endTime = secondsToTimeString(currentTime + 3); // Default 3 second duration

    addSubtitle({
      startTime,
      endTime,
      text: "",
      translatedText: ""
    });
  };

  // Handle clearing all subtitles
  const handleClearSubtitles = () => {
    console.log('[SUBTITLE_CLEAR] Manual clear requested');

    // Clear all subtitle data
    clearSubtitles();

    // Reset editing states
    setEditingField(null);
    setCurrentTime(0);
    setShowPromptInput(false);
    setPromptText("");

    // Reset processing states
    setIsGenerating(false);
    setIsTranslating(false);
    setIsExtracting(false);
    setTranslationStatus("");
    setExtractionStatus("");

    toast.success("All subtitles cleared");
  };

  // Handle seeking to specific time - update timer and optionally seek video
  const handleSeekTo = (time: number) => {
    console.log('[SEEK] Seeking to time:', time, 'videoReady:', videoReady, 'hasArtPlayer:', !!artPlayerRef.current);
    setCurrentTime(time);
    // Try to seek video player if available (but don't break if it fails)
    if (artPlayerRef.current && videoReady) {
      try {
        console.log('[SEEK] Attempting video seek to:', time);
        artPlayerRef.current.seek = time;
        console.log('[SEEK] Video seek successful');
      } catch (error) {
        // Silently ignore seek errors to prevent breaking video
        console.warn('[SEEK] Video seek failed, continuing with timer only:', error);
      }
    } else {
      console.log('[SEEK] Video seek skipped - not ready or no player');
    }
  };

  // Simple video controls - completely rewritten to avoid conflicts
  const handleVideoPlay = () => {
    console.log('[PLAY] Play button clicked - videoReady:', videoReady, 'hasArtPlayer:', !!artPlayerRef.current, 'isVideoPlaying:', isVideoPlaying, 'isTimerRunning:', isTimerRunning);

    if (artPlayerRef.current && videoReady) {
      try {
        console.log('[PLAY] Attempting to play video');
        // Don't set state here - let the video events handle it
        artPlayerRef.current.play();
        console.log('[PLAY] Video play() called successfully');
      } catch (error) {
        console.warn('[PLAY] Video play failed, using manual timer:', error);
        setIsTimerRunning(true);
        setIsVideoPlaying(false);
      }
    } else {
      // No video, just start manual timer
      console.log('[PLAY] No video available, starting manual timer');
      setIsTimerRunning(true);
      setIsVideoPlaying(false);
    }
  };

  const handleVideoPause = () => {
    console.log('[PAUSE] Pause button clicked - videoReady:', videoReady, 'hasArtPlayer:', !!artPlayerRef.current, 'isVideoPlaying:', isVideoPlaying, 'isTimerRunning:', isTimerRunning);

    if (artPlayerRef.current && videoReady) {
      try {
        console.log('[PAUSE] Attempting to pause video');
        // Don't set state here - let the video events handle it
        artPlayerRef.current.pause();
        console.log('[PAUSE] Video pause() called successfully');
      } catch (error) {
        console.warn('[PAUSE] Video pause failed:', error);
        setIsTimerRunning(false);
        setIsVideoPlaying(false);
      }
    } else {
      // No video, just stop manual timer
      console.log('[PAUSE] No video available, stopping manual timer');
      setIsTimerRunning(false);
      setIsVideoPlaying(false);
    }
  };

  const handleSyncWithVideo = () => {
    console.log('[SYNC] Sync button clicked - videoReady:', videoReady, 'hasArtPlayer:', !!artPlayerRef.current);

    if (artPlayerRef.current && videoReady) {
      try {
        const videoTime = artPlayerRef.current.currentTime || 0;
        console.log('[SYNC] Current video time:', videoTime);
        setCurrentTime(videoTime);
        toast.success(`Synced to video time: ${videoTime.toFixed(1)}s`);
      } catch (error) {
        console.error('[SYNC] Could not sync with video time:', error);
        toast.error('Could not sync with video time');
      }
    } else {
      console.warn('[SYNC] Video not ready for sync');
      toast.error('Video not ready for sync');
    }
  };

  const handleGenerateAISubtitles = async () => {
    if (!promptText.trim()) {
      toast.error("Please enter a description for the AI (e.g., 'this cooking video').");
      return;
    }

    // Get languages from current task settings
    const sourceLanguage = currentTask.settings?.sourceLanguage || "en"; // Default to 'en' if not set
    const targetLanguage = currentTask.settings?.targetLanguage || "zh"; // Default to 'zh' if not set
    const promptSourceDescription = promptText; // User's input describes the source

    const structuredPrompt = `You are an expert multilingual transcription and translation service.
    Your task is to transcribe audio from ${promptSourceDescription} and then immediately translate each line.
    The original audio is in ${sourceLanguage}.
    Translate each transcribed line into ${targetLanguage}.

    Provide the output as a sequence of subtitle entries.
    For EACH subtitle entry, you MUST follow this exact multi-line structure:
    <index_number>
    <start_timestamp> --> <end_timestamp>  (timestamp format: HH:MM:SS,mmm)
    <text_in_${sourceLanguage}>
    <text_in_${targetLanguage}>

    (Ensure there is ONE blank line separating each complete subtitle entry from the next index_number. Do not add extra blank lines within an entry.)

    Generate approximately 10-15 subtitle entries.
    Ensure timestamps are sequential and represent realistic speech segment durations (e.g., 2-7 seconds per line, with short gaps between them).
    The first subtitle should start after 00:00:00,500.

    Example of a single entry:
    1
    00:00:01,234 --> 00:00:04,567
    This is the original sentence in ${sourceLanguage}.
    This is the translated sentence in ${targetLanguage}.

    Do not include any other explanations, introductory text, or any text outside of this specified SRT-like bilingual format.
    The entire response should be only the structured subtitle entries.`;

    setIsGenerating(true);
    try {
      const apiHost = env.NEXT_PUBLIC_BASE_API_HOST || 'http://127.0.0.1:9011';
      const response = await fetch(`${apiHost}/v1/chat/completions`, {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Authorization': 'Bearer sk-xxx', // IMPORTANT: Use environment variables in production
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: [{ role: "user", content: structuredPrompt }],
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: "Unknown error occurred" }));
        throw new Error(`API error: ${response.status} ${response.statusText}. ${errorData.message || ''}`);
      }

      const responseData = await response.json();
      
      // Assuming the SRT content is in responseData.choices[0].message.content
      const srtContent = responseData?.choices?.[0]?.message?.content;

      if (!srtContent || typeof srtContent !== 'string') {
        console.error("Invalid response structure from AI:", responseData);
        throw new Error("Failed to extract SRT content from AI response.");
      }
      
      const newSubtitles = parseSrtToSubtitleItems(srtContent);

      if (newSubtitles.length === 0 && srtContent.trim() !== "") {
         // This means parsing might have failed or SRT was empty/malformed but not entirely whitespace
         toast.warning("AI generated content, but it could not be parsed into subtitles. Please check the format.");
      } else if (newSubtitles.length === 0) {
        toast.info("AI did not generate any subtitles.");
      } else {
        toast.success(`Successfully generated ${newSubtitles.length} subtitle(s)!`);
      }

      setSubtitleData((currentData: SubtitleData) => ({
        ...currentData,
        originalSubtitles: newSubtitles,
        // Create a deep copy for translatedSubtitles to ensure they are distinct objects
        // if they are meant to be independently editable or represent a different translation state.
        // If translatedText within SubtitleItem is the sole source of translation, this mapping might differ.
        translatedSubtitles: newSubtitles.map(s => ({ ...s })),
      }));
      setShowPromptInput(false); // Optionally hide prompt input after generation
      setPromptText(""); // Optionally clear prompt

    } catch (error) {
      console.error("Error generating subtitles with AI:", error);
      toast.error(`Failed to generate subtitles: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleTranslateAllWithAI = async () => {
    if (subtitleData.originalSubtitles.length === 0) {
      toast.info("No original subtitles to translate.");
      return;
    }

    // Check if there are subtitles with actual text content
    const subtitlesWithText = subtitleData.originalSubtitles.filter(sub => sub.text.trim());
    if (subtitlesWithText.length === 0) {
      toast.info("No subtitle text found to translate. Please add text to your subtitles first.");
      return;
    }

    // Get target language from current task settings
    const targetLanguage = currentTask.settings?.targetLanguage || "en";

    setIsTranslating(true);
    setTranslationStatus("Preparing subtitles for translation...");

    try {
      // Use local API host if available, otherwise fall back to relative path
      const apiHost = env.NEXT_PUBLIC_BASE_API_HOST || 'http://127.0.0.1:9011';

      // Step 1: Generate SRT content from current subtitles
      const srtContent = generateSrtContent(subtitleData.originalSubtitles);
      console.log('[TRANSLATE] Generated SRT content:', srtContent);

      // Step 2: Upload SRT file
      setTranslationStatus("Uploading subtitles...");
      const uploadedUrl = await uploadSrtFile(srtContent, apiHost);
      console.log('[TRANSLATE] Uploaded SRT URL:', uploadedUrl);

      // Step 3: Call translate API with uploaded URL
      setTranslationStatus("Starting translation...");
      const translateEndpoint = `${apiHost}/translate_srt`;

      console.log('[TRANSLATE] Calling translate API:', {
        endpoint: translateEndpoint,
        uploadedUrl,
        targetLanguage
      });

      const response = await fetch(translateEndpoint, {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: uploadedUrl,
          target_language: targetLanguage,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: "Unknown error occurred" }));
        throw new Error(`API error: ${response.status} ${response.statusText}. ${errorData.message || ''}`);
      }

      const responseData = await response.json();
      console.log('[TRANSLATE] API Response:', responseData);

      // Check if we got a task_id from the response
      if (responseData.task_id) {
        console.log('[TRANSLATE] Translation started, task_id:', responseData.task_id);
        toast.success("Translation started! Polling for results...");

        // Start polling for task status
        await pollTranslationTaskStatus(responseData.task_id, apiHost);
      } else {
        console.error('[TRANSLATE] No task_id in response:', responseData);
        toast.error("Invalid response from translation API");
        // Reset loading state since we won't be polling
        setIsTranslating(false);
        setTranslationStatus("");
      }

    } catch (error) {
      console.error("Error translating subtitles:", error);
      toast.error(`Failed to translate subtitles: ${error instanceof Error ? error.message : String(error)}`);
      // Reset loading state on error
      setIsTranslating(false);
      setTranslationStatus("");
    }
    // Note: Don't reset isTranslating here - let pollTranslationTaskStatus handle it on completion
  };

  // Function to poll translation task status and handle completion
  const pollTranslationTaskStatus = async (taskId: string, apiHost: string): Promise<void> => {
    const maxAttempts = 60; // Maximum polling attempts (5 minutes with 5-second intervals)
    const pollInterval = 5000; // 5 seconds
    let attempts = 0;

    const poll = async (): Promise<void> => {
      try {
        attempts++;
        const statusEndpoint = `${apiHost}/task_status?task_id=${taskId}`;

        console.log(`[TRANSLATE] Checking task status ${attempts}:`, statusEndpoint);

        const response = await fetch(statusEndpoint, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(`Status check failed: ${response.status} ${response.statusText}`);
        }

        const statusData = await response.json();
        console.log(`[TRANSLATE] Task status check ${attempts}:`, statusData);

        if (statusData.code === 0 && statusData.data && statusData.data.url && statusData.data.url.length > 0) {
          // Task completed successfully
          const srtUrl = statusData.data.url[0]; // Get the first URL
          console.log('[TRANSLATE] Task completed, SRT URL:', srtUrl);
          setTranslationStatus("Loading translated subtitles...");
          toast.success("Translation completed! Loading subtitles...");

          // Fetch and parse the SRT content
          await fetchAndParseTranslatedSrt(srtUrl);
          setIsTranslating(false); // Reset loading state on success
          setTranslationStatus(""); // Clear status on success
          return;
        } else if (statusData.code === -1) {
          // Task is in queue or processing - show status message
          const statusMessage = statusData.msg || 'Translation in progress';
          console.log('[TRANSLATE] Task status:', statusMessage);

          // Update status display
          setTranslationStatus(statusMessage);

          // Show status message to user (less frequently to avoid spam)
          if (attempts === 1 || attempts % 6 === 0) { // Show on first check and every 30 seconds
            toast.info(`Processing: ${statusMessage}`);
          }

          if (attempts >= maxAttempts) {
            throw new Error('Translation polling timeout - maximum attempts reached');
          }

          // Continue polling
          setTimeout(poll, pollInterval);
        } else if (statusData.code !== 0) {
          // Task failed with other error codes
          throw new Error(statusData.msg || `Translation failed with code: ${statusData.code}`);
        } else {
          // Task still in progress (code 0 but no data yet)
          if (attempts >= maxAttempts) {
            throw new Error('Translation polling timeout - maximum attempts reached');
          }

          // Update status display for general progress
          setTranslationStatus(`Processing... (${attempts}/${maxAttempts} checks)`);

          // Show progress feedback every 10 attempts (50 seconds)
          if (attempts % 10 === 0) {
            toast.info(`Still processing translation... (${attempts}/${maxAttempts} checks)`);
          }

          // Continue polling
          setTimeout(poll, pollInterval);
        }
      } catch (error) {
        console.error('[TRANSLATE] Error polling task status:', error);
        toast.error(`Translation status check failed: ${error instanceof Error ? error.message : String(error)}`);
        setIsTranslating(false); // Reset loading state on error
        setTranslationStatus(""); // Clear status on error
        throw error;
      }
    };

    // Start polling
    await poll();
  };

  // Function to fetch and parse translated SRT content
  const fetchAndParseTranslatedSrt = async (srtUrl: string): Promise<void> => {
    try {
      console.log('[TRANSLATE] Fetching translated SRT from:', srtUrl);
      const response = await fetch(srtUrl);
      if (!response.ok) {
        throw new Error(`Failed to fetch translated SRT: ${response.status} ${response.statusText}`);
      }

      const srtContent = await response.text();
      console.log('[TRANSLATE] SRT content fetched, length:', srtContent.length);
      console.log('[TRANSLATE] SRT content:', srtContent);

      // Parse SRT content into subtitle items using the translated parser
      const translatedItems = parseTranslatedSrtToSubtitleItems(srtContent);
      console.log('[TRANSLATE] Parsed translated items:', translatedItems);

      if (translatedItems.length === 0) {
        toast.warning("No translated subtitles found in the response.");
        return;
      }

      // Update subtitle data - match by index position
      setSubtitleData((currentData: SubtitleData) => {
        console.log('[TRANSLATE] Current subtitle data:', currentData);
        console.log('[TRANSLATE] Original subtitles count:', currentData.originalSubtitles.length);
        console.log('[TRANSLATE] Translated items count:', translatedItems.length);

        // Check if counts match
        if (translatedItems.length !== currentData.originalSubtitles.length) {
          console.warn('[TRANSLATE] Mismatch in subtitle counts - original:', currentData.originalSubtitles.length, 'translated:', translatedItems.length);
          toast.warning(`Translation count mismatch: ${currentData.originalSubtitles.length} original vs ${translatedItems.length} translated. Some subtitles may not be translated.`);
        } else {
          toast.success(`Successfully translated ${translatedItems.length} subtitle(s)!`);
        }

        const updatedOriginals = currentData.originalSubtitles.map((originalSub, index) => {
          const translatedMatch = translatedItems[index]; // Match by index
          console.log(`[TRANSLATE] Processing subtitle ${index}:`, {
            original: originalSub,
            translated: translatedMatch
          });

          if (translatedMatch) {
            const updated = {
              ...originalSub,
              // Keep original text as is, only update translatedText
              translatedText: translatedMatch.translatedText || "",
            };
            console.log(`[TRANSLATE] Updated subtitle ${index}:`, updated);
            return updated;
          }
          return originalSub;
        });

        console.log('[TRANSLATE] Final updated originals:', updatedOriginals);

        return {
          ...currentData,
          originalSubtitles: updatedOriginals,
          translatedSubtitles: updatedOriginals.map(s => ({...s, translatedText: s.translatedText || ""})),
        };
      });

    } catch (error) {
      console.error('[TRANSLATE] Error fetching translated SRT content:', error);
      toast.error(`Failed to load translated subtitles: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  };

  // Function to poll task status and handle completion (for audio extraction)
  const pollTaskStatus = async (taskId: string, apiHost: string): Promise<void> => {
    const maxAttempts = 60; // Maximum polling attempts (5 minutes with 5-second intervals)
    const pollInterval = 5000; // 5 seconds
    let attempts = 0;

    const poll = async (): Promise<void> => {
      try {
        attempts++;
        const statusEndpoint = apiHost ? `${apiHost}/task_status?task_id=${taskId}` : `/api/task_status?task_id=${taskId}`;

        const response = await fetch(statusEndpoint, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(`Status check failed: ${response.status} ${response.statusText}`);
        }

        const statusData = await response.json();
        console.log(`Task status check ${attempts}:`, statusData);

        if (statusData.code === 0 && statusData.data && statusData.data.url && statusData.data.url.length > 0) {
          // Task completed successfully
          const srtUrl = statusData.data.url[0]; // Get the first URL
          console.log('Task completed, SRT URL:', srtUrl);
          setExtractionStatus("Loading subtitles...");
          toast.success("Audio extraction completed! Loading subtitles...");

          // Fetch and parse the SRT content
          await fetchAndParseSrt(srtUrl);
          setIsExtracting(false); // Reset loading state on success
          setExtractionStatus(""); // Clear status on success
          return;
        } else if (statusData.code === -1) {
          // Task is in queue or processing - show status message
          const statusMessage = statusData.msg || 'Task in progress';
          console.log('Task status:', statusMessage);

          // Update status display
          setExtractionStatus(statusMessage);

          // Show status message to user (less frequently to avoid spam)
          if (attempts === 1 || attempts % 6 === 0) { // Show on first check and every 30 seconds
            toast.info(`Processing: ${statusMessage}`);
          }

          if (attempts >= maxAttempts) {
            throw new Error('Task polling timeout - maximum attempts reached');
          }

          // Continue polling
          setTimeout(poll, pollInterval);
        } else if (statusData.code !== 0) {
          // Task failed with other error codes
          throw new Error(statusData.msg || `Task failed with code: ${statusData.code}`);
        } else {
          // Task still in progress (code 0 but no data yet)
          if (attempts >= maxAttempts) {
            throw new Error('Task polling timeout - maximum attempts reached');
          }

          // Update status display for general progress
          setExtractionStatus(`Processing... (${attempts}/${maxAttempts} checks)`);

          // Show progress feedback every 10 attempts (50 seconds)
          if (attempts % 10 === 0) {
            toast.info(`Still processing audio extraction... (${attempts}/${maxAttempts} checks)`);
          }

          // Continue polling
          setTimeout(poll, pollInterval);
        }
      } catch (error) {
        console.error('Error polling task status:', error);
        toast.error(`Task status check failed: ${error instanceof Error ? error.message : String(error)}`);
        setIsExtracting(false); // Reset loading state on error
        setExtractionStatus(""); // Clear status on error
        throw error;
      }
    };

    // Start polling
    await poll();
  };

  // Function to fetch and parse SRT content
  const fetchAndParseSrt = async (srtUrl: string): Promise<void> => {
    try {
      const response = await fetch(srtUrl);
      if (!response.ok) {
        throw new Error(`Failed to fetch SRT: ${response.status} ${response.statusText}`);
      }

      const srtContent = await response.text();
      console.log('SRT content fetched, length:', srtContent.length);

      // Parse SRT content into subtitle items
      const newSubtitles = parseSrtToSubtitleItems(srtContent);

      if (newSubtitles.length === 0) {
        toast.warning("No subtitles found in the extracted content.");
      } else {
        toast.success(`Successfully extracted ${newSubtitles.length} subtitle(s) from audio!`);
      }

      // Update subtitle data
      setSubtitleData((currentData: SubtitleData) => ({
        ...currentData,
        originalSubtitles: newSubtitles,
        translatedSubtitles: newSubtitles.map(s => ({ ...s })),
      }));

    } catch (error) {
      console.error('Error fetching SRT content:', error);
      toast.error(`Failed to load subtitles: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  };

  const handleExtractSubtitleFromAudio = async () => {
    // Get the video URL from current task
    const videoUrl = currentTask.DownloadTask?.videoUrl && currentTask.DownloadTask?.downloadStatus === 'success'
      ? currentTask.DownloadTask.videoUrl
      : currentTask.videoUrl;

    if (!videoUrl) {
      toast.error("No video URL available for audio extraction.");
      return;
    }

    // Get source language from current task settings
    const sourceLanguage = currentTask.settings?.sourceLanguage || "zh"; // Default to 'zh' as per the example

    setIsExtracting(true);
    setExtractionStatus("Starting audio extraction...");

    try {
      // Use local API host if available, otherwise fall back to relative path
      const apiHost = env.NEXT_PUBLIC_BASE_API_HOST || '';
      const recognEndpoint = apiHost ? `${apiHost}/recogn` : '/api/recogn';

      const response = await fetch(recognEndpoint, {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: videoUrl,
          detect_language: sourceLanguage,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: "Unknown error occurred" }));
        throw new Error(`API error: ${response.status} ${response.statusText}. ${errorData.message || ''}`);
      }

      const responseData = await response.json();

      // Check if we got a task_id from the response
      if (responseData.task_id) {
        console.log('Audio extraction started, task_id:', responseData.task_id);
        toast.success("Audio extraction started! Polling for results...");

        // Start polling for task status
        await pollTaskStatus(responseData.task_id, apiHost);
      } else {
        console.error('No task_id in response:', responseData);
        toast.error("Invalid response from audio extraction API");
        // Reset loading state since we won't be polling
        setIsExtracting(false);
        setExtractionStatus("");
      }

    } catch (error) {
      console.error("Error extracting subtitle from audio:", error);
      toast.error(`Failed to extract subtitle from audio: ${error instanceof Error ? error.message : String(error)}`);
      // Reset loading state on error
      setIsExtracting(false);
      setExtractionStatus("");
    }
    // Note: Don't reset isExtracting here - let pollTaskStatus handle it on completion
  };

  const handleBack = () => {
    stackRef.current?.pop();
  };

  // Memoize the video URL to prevent unnecessary re-renders
  const finalVideoUrl = useMemo(() => {
    const videoUrl = currentTask.DownloadTask?.videoUrl && currentTask.DownloadTask?.downloadStatus === 'success'
      ? currentTask.DownloadTask.videoUrl
      : currentTask.videoUrl;

    if (!videoUrl) return null;

    // Validate URL format before processing
    try {
      new URL(videoUrl);
    } catch (error) {
      console.error('[VIDEO_URL] Invalid video URL format:', videoUrl, error);
      return null;
    }

    // Use proxy for external URLs, direct URL for local/uploaded videos
    const shouldUseProxy = false; // videoUrl.startsWith('http') && !videoUrl.includes(window.location.hostname);
    const finalUrl = shouldUseProxy
      ? `/api/302/vt/video/proxy?url=${encodeURIComponent(videoUrl)}`
      : videoUrl;

    console.log('[VIDEO_URL] Final video URL:', finalUrl, 'shouldUseProxy:', shouldUseProxy);
    return finalUrl;
  }, [currentTask.DownloadTask?.videoUrl, currentTask.DownloadTask?.downloadStatus, currentTask.videoUrl]);

  // Memoize the poster URL to prevent unnecessary re-renders
  const posterUrl = useMemo(() => {
    if (!currentTask.thumbnail) return undefined;

    // Convert base64 data URLs to blob URLs to avoid header size issues
    if (currentTask.thumbnail.startsWith('data:')) {
      try {
        // Extract the base64 data and mime type
        const [header, base64Data] = currentTask.thumbnail.split(',');
        const mimeType = header.match(/data:([^;]+)/)?.[1] || 'image/jpeg';

        // Convert base64 to blob
        const byteCharacters = atob(base64Data);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: mimeType });

        // Create blob URL
        const blobUrl = URL.createObjectURL(blob);
        console.log('[THUMBNAIL] Converted base64 to blob URL:', blobUrl);
        return blobUrl;
      } catch (error) {
        console.error('[THUMBNAIL] Failed to convert base64 to blob URL:', error);
        // Fallback to proxy URL if conversion fails
        return `/api/302/vt/image/proxy?url=${encodeURIComponent(currentTask.thumbnail)}`;
      }
    }

    // For regular URLs, use proxy
    return `/api/302/vt/image/proxy?url=${encodeURIComponent(currentTask.thumbnail)}`;
  }, [currentTask.thumbnail]);

  // Cleanup blob URLs to prevent memory leaks
  useEffect(() => {
    return () => {
      if (posterUrl && posterUrl.startsWith('blob:')) {
        console.log('[THUMBNAIL] Cleaning up blob URL:', posterUrl);
        URL.revokeObjectURL(posterUrl);
      }
    };
  }, [posterUrl]);

  // Stable getInstance callback to prevent ArtPlayer recreation
  const getInstance = useCallback((art: any) => {
    // Store reference for manual control
    artPlayerRef.current = art;
    console.log('[ARTPLAYER] Instance created:', art);
    console.log('[ARTPLAYER] Initial state - playing:', art.playing, 'currentTime:', art.currentTime);

    // Set up event listeners properly
    art.on('ready', () => {
      console.log('[ARTPLAYER] Video is ready');
      console.log('[ARTPLAYER] Video duration:', art.duration);
      console.log('[ARTPLAYER] Video URL:', art.url);
      setVideoReady(true);
    });

    // Only track time updates, don't interfere with play/pause
    art.on('video:timeupdate', () => {
      if (art.playing) {
        const currentVideoTime = art.currentTime;
        console.log('[ARTPLAYER] Time update:', currentVideoTime.toFixed(1), 'playing:', art.playing);
        setCurrentTime(currentVideoTime);
      }
    });

    // Track play/pause state from actual video events
    art.on('video:play', () => {
      console.log('[ARTPLAYER] Video play event fired');
      console.log('[ARTPLAYER] Video state - playing:', art.playing, 'currentTime:', art.currentTime);
      setIsVideoPlaying(true);
      setIsTimerRunning(false); // Stop manual timer when video plays
    });

    art.on('video:pause', () => {
      console.log('[ARTPLAYER] Video pause event fired');
      console.log('[ARTPLAYER] Video state - playing:', art.playing, 'currentTime:', art.currentTime);
      setIsVideoPlaying(false);
    });

    art.on('video:ended', () => {
      console.log('[ARTPLAYER] Video ended event');
      setIsVideoPlaying(false);
    });

    art.on('video:canplay', () => {
      console.log('[ARTPLAYER] Video can play');
    });

    art.on('video:waiting', () => {
      console.log('[ARTPLAYER] Video waiting/buffering');
    });

    // Handle video errors gracefully
    art.on('video:error', (error: any) => {
      console.error('[ARTPLAYER] Video error:', error);
      setVideoReady(false);
      setIsVideoPlaying(false);
    });
  }, []); // Empty dependency array - this callback should never change

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center gap-3 border-b bg-card/50 p-3">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleBack}
          className="h-7 w-7 p-0"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div className="flex items-center gap-2 flex-1">
          <Edit3 className="h-4 w-4 text-primary" />
          <h2 className="text-sm font-medium">
            {t("form.fields.subtitleEditor.title")} with Video Preview
          </h2>
        </div>
      </div>

      {/* Main Content - Split Layout */}
      <div className="flex-1 flex">
        {/* Video Preview Panel */}
        <div className="w-1/2 border-r relative">
          {/* Video Player */}
          <div
            className={cn(
              "p-4 sticky top-5 z-20 transition-all duration-300 ease-in-out",
              isVideoFollowing
                ? "shadow-xl backdrop-blur-md border border-primary/30 bg-card/80 rounded-xl"
                : "bg-transparent rounded-lg"
            )}
            ref={videoContainerRef}
          >
            <div className="relative aspect-video bg-black rounded-lg overflow-hidden">
              {(() => {
                // Handle case where we have thumbnail but no video URL (thumbnail preview)
                if (!finalVideoUrl && currentTask.thumbnail) {
                  return (
                    <div className="relative w-full h-full">
                      <Image
                        src={posterUrl || currentTask.thumbnail}
                        alt={currentTask.name || "Video thumbnail"}
                        fill
                        className="object-contain"
                        sizes="(max-width: 768px) 100vw, 50vw"
                        priority
                      />
                      <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                        <div className="text-center text-white">
                          <div className="text-sm font-medium">Video Preview</div>
                          <div className="text-xs opacity-75">Thumbnail only</div>
                        </div>
                      </div>
                    </div>
                  );
                }

                // Handle video playback
                if (finalVideoUrl) {
                  const isYoutube = isYoutubeUrl(finalVideoUrl);
                  const youtubeVideoId = isYoutube ? getYoutubeVideoId(finalVideoUrl) : null;

                  if (isYoutube && youtubeVideoId) {
                    return (
                      <iframe
                        src={`https://www.youtube.com/embed/${youtubeVideoId}`}
                        className="absolute inset-0 size-full"
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                        allowFullScreen
                      />
                    );
                  } else {
                    return (
                      <div className="relative w-full h-full">
                        <ArtPlayer
                          key={finalVideoUrl} // Force recreation only when URL changes
                          url={finalVideoUrl}
                          poster={posterUrl}
                          className="absolute inset-0 size-full"
                          getInstance={getInstance}
                        />
                        {/* Add subtitle overlay back but in a non-interfering way */}
                        <div className="absolute inset-0 pointer-events-none flex flex-col justify-end p-4 z-10">
                          {currentSubtitle && (currentSubtitle.text || currentSubtitle.translatedText) && (
                            <>
                              {currentSubtitle.translatedText && (
                                <div className="text-center mb-2">
                                  <div className="inline-block bg-black/80 text-white px-3 py-1 rounded text-sm font-medium shadow-lg">
                                    {currentSubtitle.translatedText}
                                  </div>
                                </div>
                              )}
                              {currentSubtitle.text && (
                                <div className="text-center">
                                  <div className="inline-block bg-black/80 text-white px-3 py-1 rounded text-sm shadow-lg">
                                    {currentSubtitle.text}
                                  </div>
                                </div>
                              )}
                            </>
                          )}
                        </div>
                      </div>
                    );
                  }
                }

                // Default fallback - no video or thumbnail
                return (
                  <div className="w-full h-full flex items-center justify-center text-white">
                    <div className="text-center space-y-2">
                      <div className="text-sm font-medium">No video loaded</div>
                      <div className="text-xs text-muted-foreground">Please select a video first</div>
                    </div>
                  </div>
                );
              })()}
            </div>

            {/* Manual Time Control */}
            <div className="mt-3 space-y-2">
              <div className="text-sm text-muted-foreground">
                <div className="flex items-center justify-between">
                  <span>Manual Time: {currentTime.toFixed(1)}s</span>
                  <div className="flex items-center gap-2">
                    {videoReady && (
                      <span className="text-green-600 text-xs">● Video Ready</span>
                    )}
                    {isVideoFollowing && (
                      <span className="text-blue-600 text-xs animate-pulse">📍 Following Scroll</span>
                    )}
                    {currentSubtitle && (
                      <span className="text-primary font-medium">
                        Active: {currentSubtitle.startTime} - {currentSubtitle.endTime}
                      </span>
                    )}
                  </div>
                </div>
              </div>

              {/* Enhanced manual time input and controls with video integration */}
              <div className="flex items-center gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    if (isVideoPlaying || isTimerRunning) {
                      handleVideoPause();
                    } else {
                      handleVideoPlay();
                    }
                  }}
                  className="h-8 w-8 p-0"
                  title="Play/Pause video and timer"
                >
                  {(isVideoPlaying || isTimerRunning) ? <Pause className="h-3 w-3" /> : <Play className="h-3 w-3" />}
                </Button>
                <Input
                  type="number"
                  value={currentTime}
                  onChange={(e) => {
                    const newTime = parseFloat(e.target.value) || 0;
                    console.log('[MANUAL] Time input changed to:', newTime);
                    setCurrentTime(newTime);
                    // Also seek video when manually changing time
                    if (artPlayerRef.current && videoReady) {
                      try {
                        console.log('[MANUAL] Seeking video to manual time:', newTime);
                        artPlayerRef.current.seek = newTime;
                      } catch (error) {
                        console.warn('[MANUAL] Video seek failed during manual time change:', error);
                      }
                    } else {
                      console.log('[MANUAL] Video seek skipped - not ready');
                    }
                  }}
                  className="w-20 h-8 text-xs"
                  step="0.1"
                  min="0"
                  placeholder="0.0"
                />
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleSyncWithVideo}
                  className="h-8 w-8 p-0"
                  title="Sync timer with current video time"
                  disabled={!videoReady}
                >
                  🔄
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    console.log('[RESET] Reset button clicked');
                    setCurrentTime(0);
                    if (artPlayerRef.current && videoReady) {
                      try {
                        console.log('[RESET] Seeking video to 0');
                        artPlayerRef.current.seek = 0;
                        console.log('[RESET] Video reset successful');
                      } catch (error) {
                        console.warn('[RESET] Video reset failed:', error);
                      }
                    } else {
                      console.log('[RESET] Video reset skipped - not ready');
                    }
                  }}
                  className="h-8 text-xs"
                  title="Reset video and timer to start"
                >
                  Reset
                </Button>
                <span className="text-xs text-muted-foreground">
                  Video + Timer Control
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Subtitle Editor Panel */}
        <div className="w-1/2 flex flex-col h-screen overflow-y-auto ml-auto" ref={subtitleListRef}>
          {/* Action Bar - Sticky */}
          <div className="sticky top-0 z-10 bg-background border-b shadow-sm">
            <div className="flex items-center gap-2 bg-muted/30 p-3">
              <Button
                size="sm"
                variant="default"
                onClick={() => setShowPromptInput(!showPromptInput)}
                disabled={isGenerating || isTranslating || isExtracting}
              >
                <Wand2 className="h-3 w-3 mr-1" />
                Create with AI
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={handleExtractSubtitleFromAudio}
                disabled={isExtracting || isGenerating || isTranslating}
              >
                {isExtracting ? (
                  <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                ) : (
                  <AudioLines className="h-3 w-3 mr-1" />
                )}
                Extract from Audio
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={handleTranslateAllWithAI}
                disabled={isTranslating || isGenerating || isExtracting || subtitleData.originalSubtitles.length === 0 || !subtitleData.originalSubtitles.some(sub => sub.text.trim())}
              >
                {isTranslating ? (
                  <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                ) : (
                  <Languages className="h-3 w-3 mr-1" />
                )}
                Translate All
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={handleClearSubtitles}
                disabled={isTranslating || isGenerating || isExtracting || subtitleData.originalSubtitles.length === 0}
                className="text-destructive hover:text-destructive"
              >
                <RotateCcw className="h-3 w-3 mr-1" />
                Clear All
              </Button>
              <div className="ml-auto text-xs text-muted-foreground">
                {subtitleData.originalSubtitles.length} subtitles
              </div>
            </div>
          </div>

          {/* Extraction Status Display */}
          {isExtracting && extractionStatus && (
            <div className="border-b bg-blue-50 dark:bg-blue-950/20 p-3">
              <div className="flex items-center gap-2 text-sm text-blue-700 dark:text-blue-300">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>{extractionStatus}</span>
              </div>
            </div>
          )}

          {/* Translation Status Display */}
          {isTranslating && translationStatus && (
            <div className="border-b bg-green-50 dark:bg-green-950/20 p-3">
              <div className="flex items-center gap-2 text-sm text-green-700 dark:text-green-300">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>{translationStatus}</span>
              </div>
            </div>
          )}

          {/* AI Prompt Input */}
          {showPromptInput && (
            <div className="border-b bg-muted/20 p-4">
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Wand2 className="h-4 w-4 text-primary" />
                  <h3 className="font-medium">Create Subtitles with AI</h3>
                </div>
                <Textarea
                  value={promptText}
                  onChange={(e) => setPromptText(e.target.value)}
                  placeholder="Describe the audio source (e.g., 'this lecture on quantum physics', 'the dialogue in this short film')"
                  className="min-h-[80px]"
                />
                <div className="flex items-center gap-2">
                  <Button
                    size="sm"
                    onClick={handleGenerateAISubtitles}
                    disabled={!promptText.trim() || isGenerating || isExtracting}
                  >
                    {isGenerating ? (
                      <>
                        <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Wand2 className="h-3 w-3 mr-1" />
                        Generate
                      </>
                    )}
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowPromptInput(false)}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Content */}
          {subtitleData.originalSubtitles.length > 0 ? (
            <div className="flex-1 overflow-y-auto">
              {/* Header Row */}
              <div className="sticky top-0 z-10 bg-background border-b">
                <div className="grid grid-cols-6 gap-2 p-3 text-xs font-medium text-muted-foreground bg-muted/30">
                  <div>Start Time</div>
                  <div>End Time</div>
                  <div>Original Text</div>
                  <div>Translation</div>
                  <div>Duration</div>
                  <div>Actions</div>
                </div>
              </div>

              {/* Subtitle Rows */}
              <div className="divide-y">
                {subtitleData.originalSubtitles.map((subtitle) => (
                  <SubtitleRow
                    key={subtitle.id}
                    subtitle={subtitle}
                    editingField={editingField?.id === subtitle.id ? editingField.field : null}
                    onFieldEdit={(field) =>
                      setEditingField(field ? { id: subtitle.id, field } : null)
                    }
                    onSave={(updates) => {
                      updateBothSubtitles({ id: subtitle.id, updates });
                    }}
                    onDelete={() => deleteSubtitle(subtitle.id)}
                    isActive={currentSubtitle?.id === subtitle.id}
                    onSeekTo={handleSeekTo}
                  />
                ))}
              </div>

              {/* Add Subtitle Button */}
              <div className="p-4 border-t bg-muted/10">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleAddSubtitle}
                  className="w-full"
                >
                  <Plus className="h-3 w-3 mr-1" />
                  Add Subtitle at {currentTime.toFixed(1)}s
                </Button>
              </div>
            </div>
          ) : (
            <div className="min-h-[400px] flex items-center justify-center">
              <div className="text-center space-y-3">
                <div className="mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center">
                  <Edit3 className="h-6 w-6 text-muted-foreground" />
                </div>
                <div>
                  <h3 className="font-medium">{t("form.fields.subtitleEditor.noSubtitles")}</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    Click &quot;Add Subtitle&quot; to create your first subtitle.
                  </p>
                </div>
                <Button
                  size="sm"
                  onClick={handleAddSubtitle}
                >
                  <Plus className="h-3 w-3 mr-1" />
                  Add First Subtitle
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
