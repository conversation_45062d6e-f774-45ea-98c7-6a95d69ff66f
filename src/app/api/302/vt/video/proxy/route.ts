import { createScopedLogger } from "@/utils";
import { NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";
export const runtime = "edge";

const logger = createScopedLogger("VideoProxy");

export async function GET(request: NextRequest) {
  try {
    const url = request.nextUrl.searchParams.get("url");
    if (!url) {
      return NextResponse.json(
        { error: { message: "URL is required" } },
        { status: 400 }
      );
    }

    // Validate URL format
    let parsedUrl: URL;
    try {
      parsedUrl = new URL(url);
    } catch (error) {
      logger.error("Invalid URL format:", url, error);
      return NextResponse.json(
        { error: { message: "Invalid URL format" } },
        { status: 400 }
      );
    }

    // Log the URL being proxied for debugging
    logger.info("Proxying video URL:", url);

    // Add timeout and signal for better error handling
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

    try {
      const response = await fetch(url, {
        signal: controller.signal,
        headers: {
          "User-Agent":
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
          Accept: "*/*",
          "Accept-Language": "en-US,en;q=0.9",
          Range: request.headers.get("range") || "bytes=0-",
          Referer: parsedUrl.origin,
        },
      });

      clearTimeout(timeoutId);

      if (!response.ok && response.status !== 206) {
        logger.error(
          "Proxy error: Bad response",
          response.status,
          response.statusText,
          "for URL:",
          url
        );
        return NextResponse.json(
          { error: { message: `Failed to fetch video: ${response.status} ${response.statusText}` } },
          { status: response.status }
        );
      }

      logger.info("Successfully proxied video, status:", response.status);

      const headers = new Headers();
      headers.set("Accept-Ranges", "bytes");
      headers.set(
        "Content-Type",
        response.headers.get("Content-Type") || "video/mp4"
      );
      headers.set("Content-Length", response.headers.get("Content-Length") || "");
      headers.set("Content-Range", response.headers.get("Content-Range") || "");
      headers.set("Access-Control-Allow-Origin", "*");
      headers.set("Access-Control-Allow-Methods", "GET, HEAD, OPTIONS");
      headers.set("Access-Control-Allow-Headers", "Range");

      if (response.status === 206) {
        headers.set("Accept-Ranges", "bytes");
        headers.set("Content-Range", response.headers.get("Content-Range") || "");
      }

      return new NextResponse(response.body, {
        status: response.status,
        statusText: response.statusText,
        headers,
      });
    } catch (fetchError) {
      clearTimeout(timeoutId);
      throw fetchError;
    }
  } catch (error) {
    logger.error("Proxy error:", error);

    // Provide more specific error messages based on error type
    let errorMessage = "Failed to proxy video";
    let statusCode = 500;

    if (error instanceof TypeError && error.message.includes("fetch failed")) {
      errorMessage = "Unable to reach the video URL. The URL may be invalid or the server may be unreachable.";
      statusCode = 502; // Bad Gateway
    } else if (error instanceof Error && error.name === "AbortError") {
      errorMessage = "Video request timed out. The server may be slow or unreachable.";
      statusCode = 504; // Gateway Timeout
    } else if (error instanceof Error) {
      errorMessage = `Proxy error: ${error.message}`;
    }

    return NextResponse.json(
      { error: { message: errorMessage } },
      { status: statusCode }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, HEAD, OPTIONS",
      "Access-Control-Allow-Headers": "Range",
      "Access-Control-Max-Age": "86400",
    },
  });
}
